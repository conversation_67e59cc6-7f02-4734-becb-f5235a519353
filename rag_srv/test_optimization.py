#!/usr/bin/env python
"""
测试优化后的RAG系统
验证全局向量数据库和RAG代理实例的重用
"""

import time
import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rag_agent import (
    check_vector_db_exists,
    get_or_create_vector_db_instance,
    get_or_create_rag_agent,
    add_single_document_with_metadata,
    delete_single_document,
    reset_global_instances,
    global_vector_db,
    global_rag_agent
)

def test_vector_db_reuse():
    """测试向量数据库实例重用"""
    print("=== 测试向量数据库实例重用 ===")
    
    # 重置全局实例
    reset_global_instances()
    
    # 第一次创建
    start_time = time.time()
    db1 = get_or_create_vector_db_instance(None)
    time1 = time.time() - start_time
    print(f"第一次创建向量数据库用时: {time1:.3f}秒")
    
    # 第二次获取（应该重用）
    start_time = time.time()
    db2 = get_or_create_vector_db_instance(None)
    time2 = time.time() - start_time
    print(f"第二次获取向量数据库用时: {time2:.3f}秒")
    
    # 验证是否是同一个实例
    print(f"是否为同一实例: {db1 is db2}")
    print(f"性能提升: {(time1 - time2) / time1 * 100:.1f}%")
    
    return db1

def test_rag_agent_reuse():
    """测试RAG代理实例重用"""
    print("\n=== 测试RAG代理实例重用 ===")
    
    # 获取向量数据库
    vector_db = get_or_create_vector_db_instance(None)
    
    # 第一次创建RAG代理
    start_time = time.time()
    agent1 = get_or_create_rag_agent(vector_db, debug_mode=False)
    time1 = time.time() - start_time
    print(f"第一次创建RAG代理用时: {time1:.3f}秒")
    
    # 第二次获取（应该重用）
    start_time = time.time()
    agent2 = get_or_create_rag_agent(vector_db, debug_mode=False)
    time2 = time.time() - start_time
    print(f"第二次获取RAG代理用时: {time2:.3f}秒")
    
    # 验证是否是同一个实例
    print(f"是否为同一实例: {agent1 is agent2}")
    print(f"性能提升: {(time1 - time2) / time1 * 100:.1f}%")
    
    return agent1

def test_document_operations():
    """测试文档操作不会重新创建实例"""
    print("\n=== 测试文档操作优化 ===")
    
    # 获取初始实例
    initial_db = get_or_create_vector_db_instance(None)
    initial_agent = get_or_create_rag_agent(initial_db, debug_mode=False)
    
    print(f"初始向量数据库ID: {id(initial_db)}")
    print(f"初始RAG代理ID: {id(initial_agent)}")
    
    # 创建测试文件
    test_file = "test_doc.txt"
    with open(test_file, "w", encoding="utf-8") as f:
        f.write("这是一个测试文档，用于验证优化效果。")
    
    try:
        # 添加文档
        print("\n添加文档...")
        start_time = time.time()
        result_db = add_single_document_with_metadata(test_file, {"title": "测试文档"})
        add_time = time.time() - start_time
        print(f"添加文档用时: {add_time:.3f}秒")
        
        # 检查实例是否被重用
        current_db = get_or_create_vector_db_instance(None)
        current_agent = get_or_create_rag_agent(current_db, debug_mode=False)
        
        print(f"添加后向量数据库ID: {id(current_db)}")
        print(f"添加后RAG代理ID: {id(current_agent)}")
        
        print(f"向量数据库实例是否重用: {initial_db is current_db}")
        print(f"RAG代理实例是否重用: {initial_agent is current_agent}")
        
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

def main():
    """主测试函数"""
    print("开始测试RAG系统优化效果...")
    
    # 检查向量数据库是否存在
    if not check_vector_db_exists():
        print("警告: 向量数据库不存在，某些测试可能失败")
        print("请先运行rag_agent.py或添加一些文档")
        return
    
    try:
        # 测试向量数据库重用
        test_vector_db_reuse()
        
        # 测试RAG代理重用
        test_rag_agent_reuse()
        
        # 测试文档操作优化
        test_document_operations()
        
        print("\n=== 测试总结 ===")
        print("✅ 向量数据库实例重用正常")
        print("✅ RAG代理实例重用正常")
        print("✅ 文档操作不会重新创建实例")
        print("🎉 优化效果验证成功！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
